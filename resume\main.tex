% !TeX TS-program = xelatex

\documentclass{resume}
\ResumeName{冯开宇}

% 如果想插入照片，请使用以下两个库。
% \usepackage{graphicx}
% \usepackage{tikz}

\begin{document}

\ResumeContacts{
  (+86)188-8888-8888,%
  \ResumeUrl{mailto:<EMAIL>}{<EMAIL>},%
  \ResumeUrl{https://blog.fkynjyq.com}{blog.fkynjyq.com} \footnote{下划线内容包含超链接。},%
  \ResumeUrl{https://github.com/fky2015}{github.com/fky2015}%
}

% 如果想插入照片，请取消此代码的注释。
% 但是默认不推荐插入照片，因为这不是简历的重点。
% 如果默认的照片插入格式不能满足你的需求，你可以尝试调整照片的大小，或者使用其他的插入照片的方法。
% 不然，也可以先渲染 PDF 简历，然后用其他工具在 PDF 上叠加照片。
% \begin{tikzpicture}[remember picture, overlay]
%   \node [anchor=north east, inner sep=1cm]  at (current page.north east) 
%      {\includegraphics[width=2cm]{image.png}};
% \end{tikzpicture}

\ResumeTitle


\section{教育经历}
\ResumeItem
[北京理工大学|硕士研究生]
{北京理工大学}
[\textnormal{网络空间安全，网络空间安全学院|}  学术型硕士研究生]
[2021.09—2024.06（预计）]

\textbf{GPA: 3.62/4.0}，主要研究方向为\textbf{拜占庭共识算法}，在分布式系统领域方面有一定的研究和工程经验。\textbf{2024年应届生}。

主要研究成果为：发表在 XXX 期刊上的论文《XXX》。

\ResumeItem
[北京理工大学|本科生]
{北京理工大学}
[\textnormal{计算机科学与技术，计算机学院|} 工学学士]
[2017.09—2021.06]

\textbf{GPA: 3.7/4.0(专业前 3\%)}，获学业奖学金多次，全国大学生 XYZ 竞赛二等奖（2次），ZYX 竞赛三等奖。

\section[技术能力]{技术能力\protect\footnote{与求职岗位无关的技能省略或用灰色表示。}}
\begin{itemize}
  \item \textbf{语言}: 编程不受特定语言限制。常用 Rust, Golang, Python,C++； 熟悉 C, \GrayText{JavaScript}；了解 Lua, Java, \GrayText{TypeScript}。
  \item \textbf{工作流}: Linux, Shell, (Neo)Vim, Git, GitHub, GitLab.
  \item \textbf{其他}: 有容器化技术的实践经验，熟悉 Kubernetes 的使用。
\end{itemize}

\section{工作经历}

\ResumeItem{北京 ABCD 有限公司}
[后端开发实习生/XXXX]
[2020.10—2021.03] 

\begin{itemize}
  \item \textbf{独立负责XXX业务后端的设计、开发、测试和部署。}通过 FaaS、Kafka 等平台实现站内信模板渲染服务。向上游提供 SDK 代码，增加或升级了多种离线和在线逻辑。完成了业务对站内信的多样需求。
  \item \textbf{参与 XXX 的需求分析，系统技术方案设计；完成需求开发、灰度测试、上线和监控。}
\end{itemize}

\section{项目经历}

\ResumeItem{\textbf{BusTub} 基于 C++ 的简易单机数据库}
[ \textnormal{CMU 15-445} 课程]
\begin{itemize}
  \item 实现了基于可扩展哈希表和LRU-K的内存池管理。实现了可并发的B+树，支持乐观加锁的读写操作。
  \item 采用火山模型实现了查询、修改、连接、聚合等查询执行器，对部分查询进行了改写与下推。
  \item 采用 2PL 进行并发控制，支持死锁处理、多种隔离级别、表锁和行锁。
  \item 对数据库系统有了基本的认识和实践。
\end{itemize}


\ResumeItem{\textbf{Multi-Raft} 分布式 \textbf{KV} 存储系统}
[ \textnormal{MIT 6.824} 课程]
\begin{itemize}
  \item 实现了 Raft 协议的选举、日志复制、持久化、日志压缩等基本功能。
  \item 基于 Raft 协议实现了满足线性一致性的 KV 数据库。
  \item 采用 Multi-Raft 架构，支持数据分片，分片迁移，分片垃圾回收和分片迁移时读写优化。
  \item 对分布式系统的设计考量有了更多的认识。
\end{itemize}

\ResumeItem{\textbf{ZYX} 平台下的某某共识算法设计与实现}
[共识算法设计与实现]
[2021.11—2022.07] 

\begin{itemize}
  \item 根据 ZYX (Rust 实现的开源区块链框架) 的架构，\textbf{修改并实现某某某共识算法}。
  \item 针对系统进行性能测试，分析瓶颈，并优化吞吐量；TPS 由 1K 达到 6K。
  \item 此项目为实验室研究项目的一部分。
\end{itemize}

\ResumeItem[BIThesis 北京理工大学毕设模板集合(开源项目)]
{\ResumeUrl{https://github.com/BITNP/BIThesis}{\textbf{BIThesis} 北京理工大学毕设 \LaTeX 模板集合}}
[主要维护者（开源项目）]
[2020.04 — 今]

\begin{itemize}
  \item 根据相关排版要求，\textbf{利用 LaTeX3 (expl3) 设计了同时符合各个学位要求且支持灵活配置的宏包及多套模板}。
  \item 需求开发和问题修复采用标准工作流，引入了回归测试与基于 GitHub Actions 的测试与持续集成。
  \item 负责了什么什么；完成了怎样的结果。
\end{itemize}

\section{个人总结}

\begin{itemize}
  \item 本人乐观开朗、在校成绩优异、自驱能力强，具有良好的沟通能力和团队合作精神。
  \item 可以使用英语进行工作交流（六级成绩 XXX），平时有阅读英文书籍和口语练习的习惯。
  \item 有六年 Linux 使用经验，较为丰富的软件开发经验、开源项目贡献和维护经验。善于技术写作，持续关注互联网技术发展。
\end{itemize}


\end{document}
